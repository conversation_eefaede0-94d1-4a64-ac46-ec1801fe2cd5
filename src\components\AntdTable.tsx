import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  Modal,
  Drawer,
  Popconfirm,
  message,
  Card,
  Row,
  Col,
  Pagination,
  Tag,
} from "antd";
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  FilterOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import type { TableColumnsType } from "antd";
import type { TableRowSelection } from "antd/es/table/interface";
import type {
  TaskData,
  TaskSearchParams,
  TaskSelectionState,
  TaskModalState,
  TaskDrawerState,
  ComplexFormData,
} from "../types/task";
import { TaskService } from "../services/taskService";
import {
  TASK_STATUS_OPTIONS,
  WEEKDAY_OPTIONS,
  FREQUENCY_OPTIONS,
} from "../types/task";
import ComplexTaskForm from "./ComplexTaskForm";
import "./AntdTable.module.css";

const { Option } = Select;

/**
 * 任务管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AntdTable: React.FC = () => {
  // 表格数据状态
  const [data, setData] = useState<TaskData[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<TaskSearchParams>({});

  // 表格选择状态 - 支持跨页面选择
  const [selection, setSelection] = useState<TaskSelectionState>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 存储所有已选择的行数据（跨页面）
  const [allSelectedRows, setAllSelectedRows] = useState<
    Map<React.Key, TaskData>
  >(new Map());

  // Modal状态
  const [searchModal, setSearchModal] = useState<TaskModalState>({
    visible: false,
  });

  // 抽屉状态
  const [editDrawer, setEditDrawer] = useState<TaskDrawerState>({
    visible: false,
  });

  const [currentRecord, setCurrentRecord] = useState<TaskData | null>(null);

  // 表单实例
  const [searchForm] = Form.useForm();

  // 加载数据
  const loadData = useCallback(
    async (customParams?: Partial<TaskSearchParams>) => {
      setLoading(true);
      try {
        // 使用传入的自定义参数，如果没有则使用当前状态
        const currentPage = customParams?.current ?? pagination.current;
        const currentPageSize = customParams?.pageSize ?? pagination.pageSize;

        const params = {
          ...searchParams,
          current: currentPage,
          pageSize: currentPageSize,
          ...customParams,
        };

        const response = await TaskService.getTasks(params);
        setData(response.data);
        setTotal(response.total);
      } catch (error) {
        message.error("加载数据失败");
        console.error("加载数据失败:", error);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, pagination]
  );

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskSearchParams) => {
      console.log("搜索表单数据:", values);
      // 暂时打印搜索参数，后续接入后端接口
      const searchData = {
        name: values.name || "",
        group: values.group || "",
        status: values.status || "",
        db_type: values.db_type || "",
        timestamp: new Date().toISOString(),
      };
      console.log("格式化搜索参数:", searchData);

      // 更新搜索参数状态
      setSearchParams(values);
      setPagination({ current: 1, pageSize: 10 });
      // 重置选择状态
      setAllSelectedRows(new Map());
      setSelection({ selectedRowKeys: [], selectedRows: [] });
      // 使用搜索参数加载数据
      loadData({ current: 1, pageSize: 10, ...values });
    },
    [loadData]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchParams({});
    setPagination({ current: 1, pageSize: 10 });
    // 重置时清空选择状态
    setAllSelectedRows(new Map());
    setSelection({ selectedRowKeys: [], selectedRows: [] });
    // 重置搜索表单
    searchForm.resetFields();
    // 立即使用重置后的参数加载数据
    loadData({ current: 1, pageSize: 10 });
  }, [loadData, searchForm]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskSearchParams) => {
      setSearchParams(values);
      setPagination((prev) => ({ ...prev, current: 1 }));
      setSearchModal({ visible: false });
      // 详细查询时清空选择状态
      setAllSelectedRows(new Map());
      setSelection({ selectedRowKeys: [], selectedRows: [] });
      // 立即使用第一页和新的搜索条件加载数据
      loadData({ current: 1, ...values });
    },
    [loadData]
  );

  // 表格列定义
  const columns: TableColumnsType<TaskData> = [
    {
      title: "任务名称",
      dataIndex: "name",
      key: "name",
      width: 180,
      ellipsis: true,
      fixed: "left",
    },
    {
      title: "任务分组",
      dataIndex: "group",
      key: "group",
      width: 120,
    },
    {
      title: "执行时间",
      key: "time",
      width: 180,
      render: (_, record) => (
        <span>
          {record.start_time} - {record.end_time}
        </span>
      ),
    },
    {
      title: "星期",
      dataIndex: "weekday",
      key: "weekday",
      width: 100,
    },
    {
      title: "执行频率",
      dataIndex: "frequency",
      key: "frequency",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 100,
      render: (status: string) => (
        <Tag color={status === "enabled" ? "success" : "error"}>
          {status === "enabled" ? "启用" : "禁用"}
        </Tag>
      ),
    },
    {
      title: "重试次数",
      dataIndex: "retryNum",
      key: "retryNum",
      width: 100,
    },
    {
      title: "数据库连接",
      dataIndex: "db_connection_id",
      key: "db_connection_id",
      width: 120,
      ellipsis: true,
    },
    {
      title: "创建时间",
      dataIndex: "createTime",
      key: "createTime",
      width: 160,
    },
    {
      title: "操作",
      key: "action",
      width: 170,
      fixed: "right",
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setCurrentRecord(record);
              setEditDrawer({ visible: true });
            }}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md px-2 py-1 transition-all duration-200"
          >
            编辑
          </Button>
          <Popconfirm
            title="确认删除"
            description="确定要删除这个任务吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            placement="topRight"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md px-2 py-1 transition-all duration-200"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 更新当前页面的选择状态
  useEffect(() => {
    // 根据全局选择状态更新当前页面的选择
    const currentPageSelectedKeys = data
      .filter((item) => allSelectedRows.has(item.id))
      .map((item) => item.id);

    const currentPageSelectedRows = data.filter((item) =>
      allSelectedRows.has(item.id)
    );

    setSelection({
      selectedRowKeys: currentPageSelectedKeys,
      selectedRows: currentPageSelectedRows,
    });
  }, [data, allSelectedRows]);

  // 表格行选择配置 - 支持跨页面选择
  const rowSelection: TableRowSelection<TaskData> = {
    selectedRowKeys: selection.selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskData[]) => {
      // 更新当前页面的选择状态
      setSelection({ selectedRowKeys, selectedRows });

      // 更新全局选择状态
      const newAllSelectedRows = new Map(allSelectedRows);

      // 移除当前页面中未选中的项
      data.forEach((item) => {
        if (!selectedRowKeys.includes(item.id)) {
          newAllSelectedRows.delete(item.id);
        }
      });

      // 添加当前页面中新选中的项
      selectedRows.forEach((row) => {
        newAllSelectedRows.set(row.id, row);
      });

      setAllSelectedRows(newAllSelectedRows);
    },
    onSelectAll: (selected: boolean) => {
      const newAllSelectedRows = new Map(allSelectedRows);

      if (selected) {
        // 全选当前页面
        data.forEach((row) => {
          newAllSelectedRows.set(row.id, row);
        });
      } else {
        // 取消选择当前页面
        data.forEach((row) => {
          newAllSelectedRows.delete(row.id);
        });
      }

      setAllSelectedRows(newAllSelectedRows);

      // 更新当前页面选择状态
      const currentPageKeys = selected ? data.map((item) => item.id) : [];
      const currentPageRows = selected ? data : [];
      setSelection({
        selectedRowKeys: currentPageKeys,
        selectedRows: currentPageRows,
      });
    },
  };

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        await TaskService.deleteTask(id);
        message.success("删除成功");
        loadData();
      } catch (error) {
        message.error("删除失败");
        console.error("删除失败:", error);
      }
    },
    [loadData]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = allSelectedRows.size;
    if (totalSelected === 0) {
      message.warning("请先选择要删除的任务");
      return;
    }

    Modal.confirm({
      title: "确认批量删除",
      content: `确定要删除选中的 ${totalSelected} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: "确定",
      cancelText: "取消",
      onOk: async () => {
        try {
          const ids = Array.from(allSelectedRows.keys()).map((key) =>
            Number(key)
          );
          await TaskService.batchDeleteTasks(ids);
          message.success("批量删除成功");
          // 清空所有选择状态
          setAllSelectedRows(new Map());
          setSelection({ selectedRowKeys: [], selectedRows: [] });
          loadData();
        } catch (error) {
          message.error("批量删除失败");
          console.error("批量删除失败:", error);
        }
      },
    });
  }, [allSelectedRows, loadData]);

  // 编辑任务提交
  const handleEditSubmit = useCallback(
    async (values: ComplexFormData) => {
      if (!currentRecord) return;

      try {
        setEditDrawer((prev) => ({ ...prev, loading: true }));
        await TaskService.updateComplexForm(currentRecord.id, values);
        message.success("更新成功");
        setEditDrawer({ visible: false, loading: false });
        setCurrentRecord(null);
        loadData();
      } catch (error) {
        message.error("更新失败");
        console.error("更新失败:", error);
        setEditDrawer((prev) => ({ ...prev, loading: false }));
      }
    },
    [currentRecord, loadData]
  );

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    setEditDrawer({ visible: false });
    setCurrentRecord(null);
  }, []);

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-gray-50 to-gray-100 relative">
      {/* 主要内容区域 - 使用大Card包装 */}
      <div className="flex-1 overflow-hidden p-6">
        <div className="h-full max-w-7xl mx-auto">
          <Card className="h-full shadow-lg border-0 rounded-xl overflow-hidden">
            <div className="h-full flex flex-col">
              {/* 查询区域 - 固定高度 */}
              <div className="flex-shrink-0" style={{ height: "140px" }}>
                {/* 快速搜索区域 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <Form form={searchForm} onFinish={handleSearchFormSubmit} className="pb-4">
                    <Row gutter={[16, 16]} className="mb-4">
                      <Col xs={24} sm={12} md={6}>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-700">
                            任务名称
                          </label>
                          <Form.Item name="name" className="mb-0">
                            <Input
                              placeholder="请输入任务名称"
                              prefix={
                                <SearchOutlined className="text-gray-400" />
                              }
                              allowClear
                              className="rounded-md"
                            />
                          </Form.Item>
                        </div>
                      </Col>

                      <Col xs={24} sm={12} md={6}>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-700">
                            任务分组
                          </label>
                          <Form.Item name="group" className="mb-0">
                            <Select
                              placeholder="请选择任务分组"
                              allowClear
                              className="w-full rounded-md"
                            >
                              <Option value="系统维护">系统维护</Option>
                              <Option value="数据备份">数据备份</Option>
                              <Option value="监控告警">监控告警</Option>
                              <Option value="日志清理">日志清理</Option>
                              <Option value="性能优化">性能优化</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                      <Col xs={24} sm={12} md={6}>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-700">
                            任务状态
                          </label>
                          <Form.Item name="status" className="mb-0">
                            <Select
                              placeholder="请选择任务状态"
                              allowClear
                              className="w-full rounded-md"
                            >
                              <Option value="enabled">启用</Option>
                              <Option value="disabled">禁用</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                      <Col xs={24} sm={12} md={6}>
                        <div className="space-y-1">
                          <label className="text-sm font-medium text-gray-700">
                            数据库类型
                          </label>
                          <Form.Item name="db_type" className="mb-0">
                            <Select
                              placeholder="请选择数据库类型"
                              allowClear
                              className="w-full rounded-md"
                            >
                              <Option value="mysql">MySQL</Option>
                              <Option value="oracle">Oracle</Option>
                              <Option value="postgresql">PostgreSQL</Option>
                              <Option value="sqlserver">SQL Server</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                      <Col xs={24} sm={12} md={6}>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={() => setEditDrawer({ visible: true })}
                          className="bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700 rounded-md w-full"
                        >
                          新增任务
                        </Button>
                      </Col>
                      <Col xs={24} sm={12} md={18}>
                        <div className="flex justify-end gap-2">
                          <Button
                            type="primary"
                            htmlType="submit"
                            icon={<SearchOutlined />}
                            className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md"
                          >
                            搜索
                          </Button>
                          <Button onClick={handleReset} className="rounded-md">
                            重置
                          </Button>
                          <Button
                            icon={<FilterOutlined />}
                            onClick={() => setSearchModal({ visible: true })}
                            className="rounded-md"
                          >
                            详细查询
                          </Button>
                        </div>
                      </Col>
                    </Row>
                  </Form>
                </div>
              </div>

              {/* 中间内容区域 - 包含批量操作和表格 */}
              <div className="flex-1 flex flex-col overflow-hidden">
                {/* 批量操作栏 - 当有选中项时显示 */}
                {allSelectedRows.size > 0 && (
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-3 mb-4 flex-shrink-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span className="text-blue-700 font-medium">
                          已选择 {allSelectedRows.size} 项（跨页面选择）
                        </span>
                      </div>
                      <Space>
                        <Button
                          type="link"
                          danger
                          onClick={handleBatchDelete}
                          className="hover:bg-red-50 rounded-md px-3"
                        >
                          批量删除
                        </Button>
                        <Button
                          type="link"
                          onClick={() => {
                            // 清空所有选择状态
                            setAllSelectedRows(new Map());
                            setSelection({
                              selectedRowKeys: [],
                              selectedRows: [],
                            });
                          }}
                          className="hover:bg-gray-50 rounded-md px-3"
                        >
                          取消全选
                        </Button>
                      </Space>
                    </div>
                  </div>
                )}

                {/* 表格区域 - 固定高度确保分页始终在底部 */}
                <div
                  className="overflow-hidden"
                  style={{
                    height:
                      allSelectedRows.size > 0
                        ? "calc(100vh - 380px)" // 有批量操作栏时的高度
                        : "calc(100vh - 320px)", // 没有批量操作栏时的高度
                  }}
                >
                  <Table
                    columns={columns}
                    dataSource={data}
                    rowKey="id"
                    loading={loading}
                    pagination={false}
                    rowSelection={rowSelection}
                    scroll={{
                      x: 1200,
                      y:
                        allSelectedRows.size > 0
                          ? "calc(100vh - 380px - 55px)" // 减去表格头部高度
                          : "calc(100vh - 320px - 55px)", // 减去表格头部高度
                    }}
                    size="middle"
                    className="custom-table"
                  />
                </div>
              </div>

              {/* 分页区域 - 固定在底部 */}
              <div
                className="flex-shrink-0 border-t border-gray-200 pt-4"
                style={{ height: "80px" }}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-600 font-medium">
                      共 {total} 条数据
                    </span>
                  </div>
                  <Pagination
                    current={pagination.current}
                    pageSize={pagination.pageSize}
                    total={total}
                    showSizeChanger
                    showQuickJumper
                    showTotal={(total, range) =>
                      `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                    }
                    className="custom-pagination"
                    onChange={(page, pageSize) => {
                      const newPageSize = pageSize || 10;
                      const newPagination = {
                        current: page,
                        pageSize: newPageSize,
                      };

                      // 先更新分页状态
                      setPagination(newPagination);

                      // 立即使用新的分页参数加载数据
                      loadData({
                        current: page,
                        pageSize: newPageSize,
                      });
                    }}
                  />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 详细查询Modal */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <FilterOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={() => setSearchModal({ visible: false })}
        footer={null}
        width={800}
        className="custom-modal"
      >
        <Form
          form={searchForm}
          layout="vertical"
          onFinish={handleAdvancedSearch}
          initialValues={searchParams}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="任务名称" name="name">
                <Input placeholder="请输入任务名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="任务分组" name="group">
                <Select placeholder="请选择任务分组" allowClear>
                  <Option value="系统维护">系统维护</Option>
                  <Option value="数据备份">数据备份</Option>
                  <Option value="监控告警">监控告警</Option>
                  <Option value="日志清理">日志清理</Option>
                  <Option value="性能优化">性能优化</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="任务状态" name="status">
                <Select placeholder="请选择任务状态" allowClear>
                  {TASK_STATUS_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="执行频率" name="frequency">
                <Select placeholder="请选择执行频率" allowClear>
                  {FREQUENCY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="星期" name="weekday">
                <Select placeholder="请选择星期" allowClear>
                  {WEEKDAY_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="告警接收人" name="alert_receiver">
                <Input placeholder="请输入告警接收人" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="开始时间" name="start_time">
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="结束时间" name="end_time">
                <Input placeholder="HH:mm:ss" />
              </Form.Item>
            </Col>
          </Row>
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              onClick={() => {
                searchForm.resetFields();
                setSearchParams({});
                setSearchModal({ visible: false });
              }}
              className="rounded-md px-6"
            >
              重置
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className="bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md px-6"
            >
              查询
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className="flex items-center space-x-2">
            <EditOutlined className="text-blue-600" />
            <span className="text-lg font-semibold">
              {currentRecord ? "编辑任务" : "新增任务"}
            </span>
          </div>
        }
        width={1200}
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className="custom-drawer"
        footer={null}
      >
        <ComplexTaskForm
          initialData={
            currentRecord
              ? {
                  task_exec: {
                    name: currentRecord.name,
                    group: currentRecord.group,
                    status: currentRecord.status,
                    start_time: currentRecord.start_time,
                    end_time: currentRecord.end_time,
                    weekday: currentRecord.weekday,
                    frequency: currentRecord.frequency,
                    retryNum: currentRecord.retryNum,
                    retry_frequency: currentRecord.retry_frequency,
                    alert_task_id: currentRecord.alert_task_id,
                    alert_send_id: currentRecord.alert_send_id,
                    db_connection_id: currentRecord.db_connection_id,
                    other_info_id: currentRecord.other_info_id,
                  },
                  task_alerts: [],
                  alert_sends: [],
                  db_connection: null,
                  other_info: null,
                }
              : undefined
          }
          onSubmit={handleEditSubmit}
          onCancel={handleDrawerClose}
          onReset={() => {
            // 重置操作的回调
            searchForm.resetFields();
            message.info("表单已重置");
          }}
          loading={editDrawer.loading}
          isEdit={!!currentRecord}
        />
      </Drawer>
    </div>
  );
};

export default AntdTable;
